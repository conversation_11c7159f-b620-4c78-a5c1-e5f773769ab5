import java.text.DecimalFormat;

public class CurrencyAmount {
    private final double value;
    private final String currency;
    
    // ANSI Color Codes
    private static final String RESET = "\u001B[0m";
    private static final String GREEN = "\u001B[32m";
    private static final String YELLOW = "\u001B[33m";
    private static final String BLUE = "\u001B[34m";
    private static final String PURPLE = "\u001B[35m";
    private static final String CYAN = "\u001B[36m";
    private static final String BOLD = "\u001B[1m";
    private static final String BLINK = "\u001B[5m";
    
    public CurrencyAmount(double value, String currency) {
        this.value = value;
        this.currency = currency;
    }
    
    public void display() {
        DecimalFormat formatter = new DecimalFormat("#,##0.00");
        System.out.println(CYAN + "┌─────────────────────────────────────────────┐" + RESET);
        System.out.println(PURPLE + "│ " + BLINK + "💎" + RESET + BOLD + GREEN + " FINAL CONVERTED AMOUNT " + BLINK + PURPLE + "💎" + RESET + PURPLE + " │" + RESET);
        System.out.println(YELLOW + "│ " + BOLD + "💰 " + formatter.format(value) + " " + currency.toUpperCase() + RESET + YELLOW + " │" + RESET);
        System.out.println(CYAN + "└─────────────────────────────────────────────┘" + RESET);
    }
    
    public double getValue() {
        return value;
    }
}
