import java.util.Scanner;
import java.text.DecimalFormat;
import java.util.Random;

public class test {
    private static final double USD_TO_RIEL_RATE = 4100.0;
    private static final DecimalFormat formatter = new DecimalFormat("#,##0.00");
    private static final Random random = new Random();

    // 🌈 SUPER COOL ANSI Color Codes - Enhanced Edition! 🌈
    public static final String RESET = "\u001B[0m";
    private static final String RED = "\u001B[31m";
    public static final String GREEN = "\u001B[32m";
    private static final String YELLOW = "\u001B[33m";
    private static final String BLUE = "\u001B[34m";
    private static final String PURPLE = "\u001B[35m";
    private static final String CYAN = "\u001B[36m";
    private static final String WHITE = "\u001B[37m";
    public static final String BRIGHT_RED = "\u001B[91m";
    public static final String BRIGHT_GREEN = "\u001B[92m";
    public static final String BRIGHT_YELLOW = "\u001B[93m";
    public static final String BRIGHT_BLUE = "\u001B[94m";
    public static final String BRIGHT_PURPLE = "\u001B[95m";
    public static final String BRIGHT_CYAN = "\u001B[96m";
    public static final String BOLD = "\u001B[1m";
    private static final String BLINK = "\u001B[5m";
    private static final String UNDERLINE = "\u001B[4m";
    private static final String REVERSE = "\u001B[7m";
    private static final String BG_BLUE = "\u001B[44m";
    private static final String BG_RED = "\u001B[41m";
    private static final String BG_GREEN = "\u001B[42m";
    private static final String BG_YELLOW = "\u001B[43m";
    private static final String BG_PURPLE = "\u001B[45m";
    private static final String BG_CYAN = "\u001B[46m";

    // 🎨 Rainbow colors array for super cool effects!
    public static final String[] RAINBOW_COLORS = {
        BRIGHT_RED, BRIGHT_YELLOW, BRIGHT_GREEN, BRIGHT_CYAN, BRIGHT_BLUE, BRIGHT_PURPLE
    };
    
    // 🎬 SUPER COOL Screen Effects! 🎬
    private static void clearScreen() {
        System.out.print("\033[2J\033[H");
        System.out.flush();
    }

    // 🌈 Rainbow typewriter effect - SUPER COOL! 🌈
    private static void rainbowTypeWriter(String text, int delay) {
        for (int i = 0; i < text.length(); i++) {
            char c = text.charAt(i);
            String color = RAINBOW_COLORS[i % RAINBOW_COLORS.length];
            System.out.print(color + BOLD + c + RESET);
            try {
                Thread.sleep(delay);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
        System.out.println();
    }

    // ⚡ Enhanced typewriter with sound effects! ⚡
    private static void typeWriter(String text, int delay) {
        for (char c : text.toCharArray()) {
            System.out.print(c);
            if (c != ' ') {
                // Simulate typing sound with random pitch
                System.out.print("\u0007"); // Bell character for "sound"
            }
            try {
                Thread.sleep(delay);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
        System.out.println();
    }

    // 🎆 EPIC particle explosion effect! 🎆
    private static void particleExplosion() {
        String[] particles = {"✨", "⭐", "💫", "🌟", "✦", "✧", "⚡", "💥"};
        System.out.println(BRIGHT_YELLOW + BOLD + "\n🎆 PARTICLE EXPLOSION! 🎆" + RESET);

        for (int wave = 0; wave < 3; wave++) {
            for (int i = 0; i < 50; i++) {
                String particle = particles[random.nextInt(particles.length)];
                String color = RAINBOW_COLORS[random.nextInt(RAINBOW_COLORS.length)];
                System.out.print(color + particle + RESET);

                if (i % 10 == 9) System.out.println();

                try {
                    Thread.sleep(30);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }
            System.out.println();
        }
    }

    // 🚀 MEGA COOL loading animation with progress bar! 🚀
    private static void loadingAnimation() {
        String[] spinner = {"🌟", "⭐", "✨", "💫"};
        String[] loadingTexts = {
            "Initializing quantum processors...",
            "Connecting to satellite networks...",
            "Calculating exchange rates...",
            "Applying currency magic...",
            "Finalizing conversion..."
        };

        System.out.println(BRIGHT_CYAN + BOLD + "🚀 SUPER CONVERSION ENGINE ACTIVATED! 🚀" + RESET);

        for (String text : loadingTexts) {
            System.out.print(CYAN + text + " ");
            for (int i = 0; i < 8; i++) {
                System.out.print(BRIGHT_YELLOW + spinner[i % 4] + RESET);
                try {
                    Thread.sleep(150);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
                System.out.print("\b");
            }
            System.out.println(BRIGHT_GREEN + "✓ COMPLETE!" + RESET);
        }

        // Progress bar
        System.out.print(YELLOW + "Progress: [");
        for (int i = 0; i <= 20; i++) {
            if (i <= 15) {
                System.out.print(BRIGHT_GREEN + "█" + RESET);
            } else {
                System.out.print(BRIGHT_YELLOW + "█" + RESET);
            }
            try {
                Thread.sleep(50);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
        System.out.println(YELLOW + "] 100% DONE!" + RESET);

        particleExplosion();
    }

    // 🎨 ASCII Art Banner - SUPER COOL! 🎨
    private static void displayCoolBanner() {
        String[] banner = {
            "  ██████╗██╗   ██╗██████╗ ██████╗ ███████╗███╗   ██╗ ██████╗██╗   ██╗",
            " ██╔════╝██║   ██║██╔══██╗██╔══██╗██╔════╝████╗  ██║██╔════╝╚██╗ ██╔╝",
            " ██║     ██║   ██║██████╔╝██████╔╝█████╗  ██╔██╗ ██║██║      ╚████╔╝ ",
            " ██║     ██║   ██║██╔══██╗██╔══██╗██╔══╝  ██║╚██╗██║██║       ╚██╔╝  ",
            " ╚██████╗╚██████╔╝██║  ██║██║  ██║███████╗██║ ╚████║╚██████╗   ██║   ",
            "  ╚═════╝ ╚═════╝ ╚═╝  ╚═╝╚═╝  ╚═╝╚══════╝╚═╝  ╚═══╝ ╚═════╝   ╚═╝   ",
            "",
            " ███╗   ███╗ █████╗  ██████╗ ██╗ ██████╗    ██████╗  ██████╗ ██╗  ██╗",
            " ████╗ ████║██╔══██╗██╔════╝ ██║██╔════╝    ██╔══██╗██╔═══██╗╚██╗██╔╝",
            " ██╔████╔██║███████║██║  ███╗██║██║         ██████╔╝██║   ██║ ╚███╔╝ ",
            " ██║╚██╔╝██║██╔══██║██║   ██║██║██║         ██╔══██╗██║   ██║ ██╔██╗ ",
            " ██║ ╚═╝ ██║██║  ██║╚██████╔╝██║╚██████╗    ██████╔╝╚██████╔╝██╔╝ ██╗",
            " ╚═╝     ╚═╝╚═╝  ╚═╝ ╚═════╝ ╚═╝ ╚═════╝    ╚═════╝  ╚═════╝ ╚═╝  ╚═╝"
        };

        for (int i = 0; i < banner.length; i++) {
            String color = RAINBOW_COLORS[i % RAINBOW_COLORS.length];
            System.out.println(color + BOLD + banner[i] + RESET);
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
    }
    
    // 🎮 SUPER MEGA ULTRA COOL MAIN METHOD! 🎮
    public static void main(String[] args) {
        try (Scanner scanner = new Scanner(System.in)) {
            clearScreen();

            // 🎨 Display epic ASCII banner
            displayCoolBanner();

            // 🎆 Welcome explosion
            System.out.println(BRIGHT_YELLOW + BOLD + "\n🎆🎆🎆 WELCOME TO THE ULTIMATE EXPERIENCE! 🎆🎆🎆" + RESET);

            // 🌈 Rainbow animated header
            System.out.println(BOLD + BRIGHT_PURPLE + "╔═══════════════════════════════════════════════════════════════╗" + RESET);
            rainbowTypeWriter("║  🌟✨💫⚡ SUPER MEGA ULTRA COOL CURRENCY CONVERTER ⚡💫✨🌟  ║", 30);
            System.out.println(BOLD + BRIGHT_PURPLE + "╚═══════════════════════════════════════════════════════════════╝" + RESET);

            // 🚀 Epic welcome message
            typeWriter(BRIGHT_GREEN + "🚀 " + BOLD + UNDERLINE + "Welcome to the most LEGENDARY converter in the universe!" + RESET, 40);

            // 💎 Exchange rate display with effects
            System.out.println(BRIGHT_PURPLE + "💎✨ " + BOLD + "USD to Cambodian Riel Exchange Rate: " +
                             BRIGHT_YELLOW + BLINK + formatter.format(USD_TO_RIEL_RATE) + " KHR" + RESET);

            // 🌈 Rainbow separator
            String separator = "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━";
            for (int i = 0; i < separator.length(); i++) {
                String color = RAINBOW_COLORS[i % RAINBOW_COLORS.length];
                System.out.print(color + separator.charAt(i) + RESET);
                try {
                    Thread.sleep(10);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }
            System.out.println();

            // 💰 Input prompt with style
            System.out.print(BRIGHT_GREEN + "💰🎯 " + BOLD + REVERSE + " Enter USD amount: " + RESET +
                           BRIGHT_YELLOW + " $" + RESET);

            if (scanner.hasNextDouble()) {
                double dollarAmount = scanner.nextDouble();
                scanner.nextLine();

                if (dollarAmount >= 0) {
                    // 🚀 MEGA loading animation
                    loadingAnimation();
                    double rielAmount = dollarAmount * USD_TO_RIEL_RATE;

                    // 🎆 Results display with maximum coolness
                    System.out.println(BOLD + BRIGHT_PURPLE + BG_YELLOW + "\n🎆🎊✨ CONVERSION MAGIC COMPLETE! ✨🎊🎆" + RESET);

                    // 🌈 Rainbow result box
                    System.out.println(BRIGHT_CYAN + "╭─────────────────────────────────────────────────────────╮" + RESET);
                    System.out.printf(BRIGHT_GREEN + "│ 💵✨ " + BOLD + UNDERLINE + "USD: " + BRIGHT_YELLOW + BG_GREEN +
                                    " $%s " + RESET + BRIGHT_GREEN + "                                    │%n", formatter.format(dollarAmount));
                    System.out.printf(BRIGHT_BLUE + "│ 🇰🇭🌟 " + BOLD + UNDERLINE + "KHR: " + BRIGHT_RED + BG_BLUE +
                                    " %s Riel " + RESET + BRIGHT_BLUE + "                           │%n", formatter.format(rielAmount));
                    System.out.println(BRIGHT_CYAN + "╰─────────────────────────────────────────────────────────╯" + RESET);

                    // 🎯 Enhanced currency display
                    CurrencyAmount convertedAmount = new CurrencyAmount(rielAmount, "KHR");
                    convertedAmount.display();

                    // 🎉 Epic thank you message
                    System.out.println(BRIGHT_YELLOW + BOLD + "\n🎊🎉🎊🎉🎊🎉🎊🎉🎊🎉🎊🎉🎊🎉🎊🎉🎊🎉🎊🎉🎊🎉" + RESET);
                    rainbowTypeWriter("� THANK YOU FOR USING OUR LEGENDARY SUPER CONVERTER! 🌟", 50);
                    System.out.println(BRIGHT_YELLOW + BOLD + "🎊🎉🎊🎉🎊🎉🎊🎉🎊🎉🎊🎉🎊🎉🎊🎉🎊🎉🎊🎉🎊🎉" + RESET);

                    // 🎆 Final particle explosion
                    particleExplosion();

                } else {
                    System.out.println(BRIGHT_RED + BOLD + BG_YELLOW + "❌💥 ERROR: " + BLINK +
                                     " Please enter a positive amount! " + RESET);
                }
            } else {
                System.out.println(BRIGHT_RED + BOLD + BG_YELLOW + "❌💥 ERROR: " + BLINK +
                                 " Please enter a valid number! " + RESET);
                scanner.nextLine();
            }
        } catch (Exception e) {
            System.out.println(BRIGHT_RED + BOLD + BG_YELLOW + "💥🔥 An unexpected error occurred: " +
                             e.getMessage() + RESET);
        }
    }
}

// 🌟 SUPER COOL Enhanced CurrencyAmount Class! 🌟
class CurrencyAmount {
    private double amount;
    private String currency;
    private static final String[] MONEY_EMOJIS = {"💰", "💎", "🏆", "👑", "⭐", "🌟", "✨", "💫"};
    private static final java.util.Random random = new java.util.Random();

    public CurrencyAmount(double amount, String currency) {
        this.amount = amount;
        this.currency = currency;
    }

    // 🎆 MEGA COOL display method with animations! 🎆
    public void display() {
        System.out.println(test.BRIGHT_CYAN + test.BOLD + "\n🎯 ═══════════════════════════════════════════════════════ 🎯" + test.RESET);

        // 💫 Animated money emojis
        System.out.print(test.BRIGHT_YELLOW + test.BOLD + "💫 FINAL RESULT: ");
        for (int i = 0; i < 5; i++) {
            String emoji = MONEY_EMOJIS[random.nextInt(MONEY_EMOJIS.length)];
            System.out.print(emoji);
            try {
                Thread.sleep(200);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
        System.out.println(test.RESET);

        // 🌈 Rainbow amount display
        String amountStr = String.valueOf(amount);
        System.out.print(test.BRIGHT_GREEN + test.BOLD + "🏆 CONVERTED AMOUNT: ");
        for (int i = 0; i < amountStr.length(); i++) {
            String color = test.RAINBOW_COLORS[i % test.RAINBOW_COLORS.length];
            System.out.print(color + test.BOLD + amountStr.charAt(i));
            try {
                Thread.sleep(50);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
        System.out.println(" " + test.BRIGHT_PURPLE + test.BOLD + currency + test.RESET);

        // 🎊 Celebration effect
        System.out.println(test.BRIGHT_YELLOW + "🎊✨🎉✨🎊✨🎉✨🎊✨🎉✨🎊✨🎉✨🎊✨🎉✨🎊" + test.RESET);
        System.out.println(test.BRIGHT_CYAN + test.BOLD + "🎯 ═══════════════════════════════════════════════════════ 🎯" + test.RESET);
    }
}