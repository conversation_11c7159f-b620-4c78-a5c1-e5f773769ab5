import java.util.Scanner;
import java.text.DecimalFormat;

public class test {
    private static final double USD_TO_RIEL_RATE = 4100.0;
    private static final DecimalFormat formatter = new DecimalFormat("#,##0.00");
    
    // ANSI Color Codes - made public for CurrencyAmount class
    public static final String RESET = "\u001B[0m";
    private static final String RED = "\u001B[31m";
    public static final String GREEN = "\u001B[32m";
    private static final String YELLOW = "\u001B[33m";
    private static final String BLUE = "\u001B[34m";
    private static final String PURPLE = "\u001B[35m";
    private static final String CYAN = "\u001B[36m";
    public static final String BOLD = "\u001B[1m";
    private static final String BLINK = "\u001B[5m";
    private static final String BG_BLUE = "\u001B[44m";
    
    private static void clearScreen() {
        System.out.print("\033[2J\033[H");
    }
    
    private static void typeWriter(String text, int delay) {
        for (char c : text.toCharArray()) {
            System.out.print(c);
            try {
                Thread.sleep(delay);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
        System.out.println();
    }
    
    private static void loadingAnimation() {
        String[] spinner = {"|", "/", "-", "\\"};
        System.out.print(CYAN + "Processing conversion ");
        for (int i = 0; i < 20; i++) {
            System.out.print("\b" + spinner[i % 4]);
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
        System.out.println("\b✓" + RESET);
    }
    
    public static void main(String[] args) {
        try (Scanner scanner = new Scanner(System.in)) {
            clearScreen();
            
            // Animated header
            System.out.println(BOLD + YELLOW + "╔═══════════════════════════════════════════════╗" + RESET);
            typeWriter(BOLD + CYAN + "║" + BLINK + "🌟✨  SUPER COOL CURRENCY CONVERTER  ✨🌟" + RESET + BOLD + CYAN + "║" + RESET, 50);
            System.out.println(BOLD + YELLOW + "╚═══════════════════════════════════════════════╝" + RESET);
            
            System.out.println(GREEN + "🚀 " + BOLD + "Welcome to the most AWESOME converter!" + RESET);
            System.out.println(PURPLE + "💎 USD to Cambodian Riel Exchange Rate: " + YELLOW + BOLD + formatter.format(USD_TO_RIEL_RATE) + RESET);
            System.out.println(BLUE + "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━" + RESET);
            
            System.out.print(GREEN + "💰 " + BOLD + "Enter USD amount: " + YELLOW + "$" + RESET);
            
            if (scanner.hasNextDouble()) {
                double dollarAmount = scanner.nextDouble();
                scanner.nextLine();
                
                if (dollarAmount >= 0) {
                    loadingAnimation();
                    double rielAmount = dollarAmount * USD_TO_RIEL_RATE;
                    
                    System.out.println(BOLD + PURPLE + "\n🎆 ✨ CONVERSION MAGIC COMPLETE ✨ 🎆" + RESET);
                    System.out.println(CYAN + "╭─────────────────────────────────────────────╮" + RESET);
                    System.out.printf(GREEN + "│ 💵 " + BOLD + "USD: " + YELLOW + "$%s" + RESET + "%n", formatter.format(dollarAmount));
                    System.out.printf(BLUE + "│ 🇰🇭 " + BOLD + "KHR: " + RED + "%s Riel" + RESET + "%n", formatter.format(rielAmount));
                    System.out.println(CYAN + "╰─────────────────────────────────────────────╯" + RESET);
                    
                    CurrencyAmount convertedAmount = new CurrencyAmount(rielAmount, "KHR");
                    convertedAmount.display();
                    
                    typeWriter(BOLD + GREEN + BG_BLUE + "🎉🎊 THANK YOU FOR USING OUR SUPER CONVERTER! 🎊🎉" + RESET, 80);
                } else {
                    System.out.println(RED + BOLD + "❌ ERROR: " + BLINK + "Please enter a positive amount!" + RESET);
                }
            } else {
                System.out.println(RED + BOLD + "❌ ERROR: " + BLINK + "Please enter a valid number!" + RESET);
                scanner.nextLine();
            }
        } catch (Exception e) {
            System.out.println(RED + BOLD + "💥 An unexpected error occurred: " + e.getMessage() + RESET);
        }
    }
}

class CurrencyAmount {
    private double amount;
    private String currency;

    public CurrencyAmount(double amount, String currency) {
        this.amount = amount;
        this.currency = currency;
    }

    public void display() {
        System.out.println(test.BOLD + test.GREEN + "Converted Amount: " + amount + " " + currency + test.RESET);
    }
}